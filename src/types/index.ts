// 端口形状枚举
export enum PortShape {
  SQUARE = 'square',
  DIAMOND = 'diamond',
  TRIANGLE = 'triangle',
  CIRCLE = 'circle'
}

// 端口颜色枚举
export enum PortColor {
  RED = '#ff4444',
  BLUE = '#4444ff',
  GREEN = '#44ff44',
  YELLOW = '#ffff44',
  PURPLE = '#ff44ff',
  ORANGE = '#ff8844'
}

// 端口方向
export enum PortDirection {
  INPUT = 'input',   // 左侧端口
  OUTPUT = 'output'  // 右侧端口
}

// 端口接口
export interface Port {
  id: string;
  shape: PortShape;
  color: PortColor;
  direction: PortDirection;
  nodeId: string;
  position: { x: number; y: number };
  isConnected: boolean;
}

// 节点类型
export enum NodeType {
  START = 'start',     // 起点节点
  END = 'end',         // 终点节点
  PROCESS = 'process'  // 处理节点
}

// 节点接口
export interface GameNode {
  id: string;
  type: NodeType;
  position: { x: number; y: number };
  inputPorts: Port[];
  outputPorts: Port[];
  depth?: number; // 用于拓扑排序
}

// 连接接口
export interface Connection {
  id: string;
  fromPortId: string;
  toPortId: string;
  fromNodeId: string;
  toNodeId: string;
}

// 游戏状态
export interface GameState {
  nodes: GameNode[];
  connections: Connection[];
  temporaryNodes: GameNode[]; // 临时区域的节点
  isGameRunning: boolean;
  score: number;
  level: number;
}

// 游戏模式
export enum GameMode {
  TETRIS = 'tetris',     // 俄罗斯方块模式
  TURN_BASED = 'turnBased' // 回合制模式
}

// 拖拽状态
export interface DragState {
  isDragging: boolean;
  draggedItem: GameNode | null;
  draggedConnection: { fromPort: Port; currentPosition: { x: number; y: number } } | null;
}
