import { GameNode, Connection as GameConnection, Port, NodeType, PortDirection } from '../types';

// 检查两个端口是否可以连接
export function canConnectPorts(fromPort: Port, toPort: Port): boolean {
  // 必须是不同方向的端口
  if (fromPort.direction === toPort.direction) {
    return false;
  }
  
  // 必须是相同形状和颜色
  if (fromPort.shape !== toPort.shape || fromPort.color !== toPort.color) {
    return false;
  }
  
  // 不能连接到同一个节点
  if (fromPort.nodeId === toPort.nodeId) {
    return false;
  }
  
  // 端口不能已经被连接
  if (fromPort.isConnected || toPort.isConnected) {
    return false;
  }
  
  return true;
}

// 检查图是否有环（有向无环图检查）
export function hasCircularDependency(nodes: GameNode[], connections: GameConnection[]): boolean {
  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  
  // 构建邻接表
  const adjacencyList = new Map<string, string[]>();
  nodes.forEach(node => {
    adjacencyList.set(node.id, []);
  });
  
  connections.forEach(connection => {
    const fromNodeConnections = adjacencyList.get(connection.fromNodeId) || [];
    fromNodeConnections.push(connection.toNodeId);
    adjacencyList.set(connection.fromNodeId, fromNodeConnections);
  });
  
  // DFS检查环
  function dfs(nodeId: string): boolean {
    visited.add(nodeId);
    recursionStack.add(nodeId);
    
    const neighbors = adjacencyList.get(nodeId) || [];
    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        if (dfs(neighbor)) {
          return true;
        }
      } else if (recursionStack.has(neighbor)) {
        return true; // 发现环
      }
    }
    
    recursionStack.delete(nodeId);
    return false;
  }
  
  // 检查所有节点
  for (const node of nodes) {
    if (!visited.has(node.id)) {
      if (dfs(node.id)) {
        return true;
      }
    }
  }
  
  return false;
}

// 计算节点深度（拓扑排序）
export function calculateNodeDepths(nodes: GameNode[], connections: GameConnection[]): Map<string, number> {
  const depths = new Map<string, number>();
  const inDegree = new Map<string, number>();
  const adjacencyList = new Map<string, string[]>();
  
  // 初始化
  nodes.forEach(node => {
    inDegree.set(node.id, 0);
    adjacencyList.set(node.id, []);
    if (node.type === NodeType.START) {
      depths.set(node.id, 0);
    }
  });
  
  // 构建图和计算入度
  connections.forEach(connection => {
    const fromConnections = adjacencyList.get(connection.fromNodeId) || [];
    fromConnections.push(connection.toNodeId);
    adjacencyList.set(connection.fromNodeId, fromConnections);
    
    const currentInDegree = inDegree.get(connection.toNodeId) || 0;
    inDegree.set(connection.toNodeId, currentInDegree + 1);
  });
  
  // 拓扑排序
  const queue: string[] = [];
  nodes.forEach(node => {
    if (inDegree.get(node.id) === 0) {
      queue.push(node.id);
      if (!depths.has(node.id)) {
        depths.set(node.id, 0);
      }
    }
  });
  
  while (queue.length > 0) {
    const currentNodeId = queue.shift()!;
    const currentDepth = depths.get(currentNodeId) || 0;
    
    const neighbors = adjacencyList.get(currentNodeId) || [];
    neighbors.forEach(neighborId => {
      const newInDegree = (inDegree.get(neighborId) || 0) - 1;
      inDegree.set(neighborId, newInDegree);
      
      const newDepth = currentDepth + 1;
      const existingDepth = depths.get(neighborId) || 0;
      depths.set(neighborId, Math.max(existingDepth, newDepth));
      
      if (newInDegree === 0) {
        queue.push(neighborId);
      }
    });
  }
  
  return depths;
}

// 检查游戏是否可解
export function isGameSolvable(nodes: GameNode[], connections: GameConnection[]): boolean {
  // 检查是否有环
  if (hasCircularDependency(nodes, connections)) {
    return false;
  }
  
  // 检查是否有起点和终点
  const hasStart = nodes.some(node => node.type === NodeType.START);
  const hasEnd = nodes.some(node => node.type === NodeType.END);
  
  if (!hasStart || !hasEnd) {
    return false;
  }
  
  // 检查所有节点是否都能连通
  const depths = calculateNodeDepths(nodes, connections);
  const endNodes = nodes.filter(node => node.type === NodeType.END);
  
  // 所有终点节点都应该有深度值（可达）
  return endNodes.every(endNode => depths.has(endNode.id));
}

// 检查所有端口是否都已连接
export function areAllPortsConnected(nodes: GameNode[]): boolean {
  for (const node of nodes) {
    // 起点节点的输入端口可以不连接
    if (node.type !== NodeType.START) {
      if (node.inputPorts.some(port => !port.isConnected)) {
        return false;
      }
    }
    
    // 终点节点的输出端口可以不连接
    if (node.type !== NodeType.END) {
      if (node.outputPorts.some(port => !port.isConnected)) {
        return false;
      }
    }
  }
  
  return true;
}
