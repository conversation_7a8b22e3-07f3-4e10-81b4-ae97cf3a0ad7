import React from 'react';
import { Connection as GameConnection, Port } from '../types';

interface ConnectionProps {
  connection: GameConnection;
  fromPort: Port;
  toPort: Port;
  onClick?: (connection: GameConnection) => void;
}

const Connection: React.FC<ConnectionProps> = ({
  connection,
  fromPort,
  toPort,
  onClick
}) => {
  // 计算贝塞尔曲线的控制点
  const fromX = fromPort.position.x;
  const fromY = fromPort.position.y;
  const toX = toPort.position.x;
  const toY = toPort.position.y;
  
  // 控制点距离
  const controlDistance = Math.abs(toX - fromX) * 0.5;
  
  // 控制点位置
  const cp1X = fromX + controlDistance;
  const cp1Y = fromY;
  const cp2X = toX - controlDistance;
  const cp2Y = toY;
  
  // 创建路径字符串
  const pathData = `M ${fromX} ${fromY} C ${cp1X} ${cp1Y}, ${cp2X} ${cp2Y}, ${toX} ${toY}`;
  
  return (
    <g>
      {/* 连接线 */}
      <path
        d={pathData}
        stroke={fromPort.color}
        strokeWidth={3}
        fill="none"
        style={{ cursor: onClick ? 'pointer' : 'default' }}
        onClick={() => onClick && onClick(connection)}
      />
      
      {/* 连接线的点击区域（更宽，便于点击） */}
      <path
        d={pathData}
        stroke="transparent"
        strokeWidth={10}
        fill="none"
        style={{ cursor: onClick ? 'pointer' : 'default' }}
        onClick={() => onClick && onClick(connection)}
      />
      
      {/* 箭头指示方向 */}
      <defs>
        <marker
          id={`arrowhead-${connection.id}`}
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill={fromPort.color}
          />
        </marker>
      </defs>
      
      <path
        d={pathData}
        stroke={fromPort.color}
        strokeWidth={3}
        fill="none"
        markerEnd={`url(#arrowhead-${connection.id})`}
        style={{ cursor: onClick ? 'pointer' : 'default' }}
        onClick={() => onClick && onClick(connection)}
      />
    </g>
  );
};

// 临时连接线组件（拖拽时显示）
interface TempConnectionProps {
  fromPort: Port;
  toPosition: { x: number; y: number };
}

export const TempConnection: React.FC<TempConnectionProps> = ({
  fromPort,
  toPosition
}) => {
  const fromX = fromPort.position.x;
  const fromY = fromPort.position.y;
  const toX = toPosition.x;
  const toY = toPosition.y;
  
  const controlDistance = Math.abs(toX - fromX) * 0.5;
  const cp1X = fromX + controlDistance;
  const cp1Y = fromY;
  const cp2X = toX - controlDistance;
  const cp2Y = toY;
  
  const pathData = `M ${fromX} ${fromY} C ${cp1X} ${cp1Y}, ${cp2X} ${cp2Y}, ${toX} ${toY}`;
  
  return (
    <path
      d={pathData}
      stroke={fromPort.color}
      strokeWidth={3}
      strokeDasharray="5,5"
      fill="none"
      opacity={0.7}
    />
  );
};

export default Connection;
