import React from 'react';
import { PortShape } from '../types';
import type { Port as PortType } from '../types';

interface PortProps {
  port: PortType;
  onPortClick: (port: PortType) => void;
  onPortMouseDown: (port: PortType, event: React.MouseEvent) => void;
  isHighlighted?: boolean;
}

const Port: React.FC<PortProps> = ({ 
  port, 
  onPortClick, 
  onPortMouseDown, 
  isHighlighted = false 
}) => {
  const getPortShape = () => {
    const size = 16;
    const halfSize = size / 2;
    
    const baseStyle = {
      fill: port.color,
      stroke: isHighlighted ? '#ffffff' : '#000000',
      strokeWidth: isHighlighted ? 3 : 1,
      cursor: 'pointer'
    };
    
    switch (port.shape) {
      case PortShape.SQUARE:
        return (
          <rect
            x={-halfSize}
            y={-halfSize}
            width={size}
            height={size}
            style={baseStyle}
          />
        );
      
      case PortShape.CIRCLE:
        return (
          <circle
            cx={0}
            cy={0}
            r={halfSize}
            style={baseStyle}
          />
        );
      
      case PortShape.DIAMOND:
        return (
          <polygon
            points={`0,-${halfSize} ${halfSize},0 0,${halfSize} -${halfSize},0`}
            style={baseStyle}
          />
        );
      
      case PortShape.TRIANGLE:
        return (
          <polygon
            points={`0,-${halfSize} ${halfSize},${halfSize} -${halfSize},${halfSize}`}
            style={baseStyle}
          />
        );
      
      default:
        return (
          <circle
            cx={0}
            cy={0}
            r={halfSize}
            style={baseStyle}
          />
        );
    }
  };

  return (
    <g
      transform={`translate(${port.position.x}, ${port.position.y})`}
      onClick={() => onPortClick(port)}
      onMouseDown={(e) => onPortMouseDown(port, e)}
    >
      {getPortShape()}
      {port.isConnected && (
        <circle
          cx={0}
          cy={0}
          r={4}
          fill="#ffffff"
          stroke="#000000"
          strokeWidth={1}
        />
      )}
    </g>
  );
};

export default Port;
