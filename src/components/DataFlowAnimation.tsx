import React, { useState, useEffect } from 'react';
import { GameNode, Connection as GameConnection, Port } from '../types';
import { calculateNodeDepths } from '../utils/gameLogic';

interface DataFlowAnimationProps {
  nodes: GameNode[];
  connections: GameConnection[];
  isPlaying: boolean;
  onAnimationComplete?: () => void;
}

interface FlowParticle {
  id: string;
  connectionId: string;
  progress: number; // 0 to 1
  color: string;
}

const DataFlowAnimation: React.FC<DataFlowAnimationProps> = ({
  nodes,
  connections,
  isPlaying,
  onAnimationComplete
}) => {
  const [particles, setParticles] = useState<FlowParticle[]>([]);
  const [animationStep, setAnimationStep] = useState(0);

  // 获取端口信息
  const getPort = (portId: string): Port | null => {
    for (const node of nodes) {
      const port = [...node.inputPorts, ...node.outputPorts].find(p => p.id === portId);
      if (port) return port;
    }
    return null;
  };

  // 获取节点信息
  const getNode = (nodeId: string): GameNode | null => {
    return nodes.find(n => n.id === nodeId) || null;
  };

  // 计算贝塞尔曲线上的点
  const getBezierPoint = (
    fromPos: { x: number; y: number },
    toPos: { x: number; y: number },
    t: number
  ): { x: number; y: number } => {
    const controlDistance = Math.abs(toPos.x - fromPos.x) * 0.5;
    const cp1 = { x: fromPos.x + controlDistance, y: fromPos.y };
    const cp2 = { x: toPos.x - controlDistance, y: toPos.y };

    // 三次贝塞尔曲线公式
    const x = Math.pow(1 - t, 3) * fromPos.x +
              3 * Math.pow(1 - t, 2) * t * cp1.x +
              3 * (1 - t) * Math.pow(t, 2) * cp2.x +
              Math.pow(t, 3) * toPos.x;

    const y = Math.pow(1 - t, 3) * fromPos.y +
              3 * Math.pow(1 - t, 2) * t * cp1.y +
              3 * (1 - t) * Math.pow(t, 2) * cp2.y +
              Math.pow(t, 3) * toPos.y;

    return { x, y };
  };

  // 开始动画
  useEffect(() => {
    if (!isPlaying) {
      setParticles([]);
      setAnimationStep(0);
      return;
    }

    // 计算节点深度以确定执行顺序
    const depths = calculateNodeDepths(nodes, connections);
    const maxDepth = Math.max(...Array.from(depths.values()));

    let currentStep = 0;
    const stepDuration = 1000; // 每步1秒

    const interval = setInterval(() => {
      if (currentStep <= maxDepth) {
        // 为当前深度的连接创建粒子
        const currentConnections = connections.filter(conn => {
          const fromNodeDepth = depths.get(conn.fromNodeId) || 0;
          return fromNodeDepth === currentStep;
        });

        const newParticles: FlowParticle[] = currentConnections.map(conn => {
          const fromPort = getPort(conn.fromPortId);
          return {
            id: `${conn.id}-${currentStep}`,
            connectionId: conn.id,
            progress: 0,
            color: fromPort?.color || '#666'
          };
        });

        setParticles(prev => [...prev, ...newParticles]);
        setAnimationStep(currentStep);
        currentStep++;
      } else {
        clearInterval(interval);
        setTimeout(() => {
          setParticles([]);
          setAnimationStep(0);
          onAnimationComplete?.();
        }, 2000);
      }
    }, stepDuration);

    return () => clearInterval(interval);
  }, [isPlaying, nodes, connections, onAnimationComplete]);

  // 更新粒子位置
  useEffect(() => {
    if (particles.length === 0) return;

    const animationInterval = setInterval(() => {
      setParticles(prev => 
        prev.map(particle => ({
          ...particle,
          progress: Math.min(particle.progress + 0.02, 1)
        })).filter(particle => particle.progress < 1)
      );
    }, 50);

    return () => clearInterval(animationInterval);
  }, [particles.length]);

  return (
    <g>
      {particles.map(particle => {
        const connection = connections.find(c => c.id === particle.connectionId);
        if (!connection) return null;

        const fromPort = getPort(connection.fromPortId);
        const toPort = getPort(connection.toPortId);
        const fromNode = getNode(connection.fromNodeId);
        const toNode = getNode(connection.toNodeId);

        if (!fromPort || !toPort || !fromNode || !toNode) return null;

        const fromPos = {
          x: fromNode.position.x + fromPort.position.x,
          y: fromNode.position.y + fromPort.position.y
        };

        const toPos = {
          x: toNode.position.x + toPort.position.x,
          y: toNode.position.y + toPort.position.y
        };

        const currentPos = getBezierPoint(fromPos, toPos, particle.progress);

        return (
          <g key={particle.id}>
            {/* 粒子光晕 */}
            <circle
              cx={currentPos.x}
              cy={currentPos.y}
              r={8}
              fill={particle.color}
              opacity={0.3}
            />
            {/* 粒子核心 */}
            <circle
              cx={currentPos.x}
              cy={currentPos.y}
              r={4}
              fill={particle.color}
              opacity={0.8}
            />
            {/* 粒子中心 */}
            <circle
              cx={currentPos.x}
              cy={currentPos.y}
              r={2}
              fill="white"
              opacity={1}
            />
          </g>
        );
      })}
    </g>
  );
};

export default DataFlowAnimation;
