import { v4 as uuidv4 } from 'uuid';
import { NodeType, PortShape, PortColor, PortDirection } from '../types';
import type { GameNode, Port } from '../types';

// 随机选择端口形状
function getRandomPortShape(): PortShape {
  const shapes = Object.values(PortShape);
  return shapes[Math.floor(Math.random() * shapes.length)];
}

// 随机选择端口颜色
function getRandomPortColor(): PortColor {
  const colors = Object.values(PortColor);
  return colors[Math.floor(Math.random() * colors.length)];
}

// 创建端口
function createPort(
  nodeId: string,
  direction: PortDirection,
  shape: PortShape,
  color: PortColor,
  position: { x: number; y: number }
): Port {
  return {
    id: uuidv4(),
    shape,
    color,
    direction,
    nodeId,
    position,
    isConnected: false
  };
}

// 创建起点节点
export function createStartNode(position: { x: number; y: number }): GameNode {
  const nodeId = uuidv4();
  const outputPorts: Port[] = [];
  
  // 起点节点有1-3个输出端口
  const portCount = Math.floor(Math.random() * 3) + 1;
  
  for (let i = 0; i < portCount; i++) {
    const portPosition = {
      x: position.x + 120, // 节点右侧
      y: position.y + 20 + i * 30
    };
    
    outputPorts.push(createPort(
      nodeId,
      PortDirection.OUTPUT,
      getRandomPortShape(),
      getRandomPortColor(),
      portPosition
    ));
  }
  
  return {
    id: nodeId,
    type: NodeType.START,
    position,
    inputPorts: [],
    outputPorts
  };
}

// 创建终点节点
export function createEndNode(position: { x: number; y: number }): GameNode {
  const nodeId = uuidv4();
  const inputPorts: Port[] = [];
  
  // 终点节点有1-3个输入端口
  const portCount = Math.floor(Math.random() * 3) + 1;
  
  for (let i = 0; i < portCount; i++) {
    const portPosition = {
      x: position.x, // 节点左侧
      y: position.y + 20 + i * 30
    };
    
    inputPorts.push(createPort(
      nodeId,
      PortDirection.INPUT,
      getRandomPortShape(),
      getRandomPortColor(),
      portPosition
    ));
  }
  
  return {
    id: nodeId,
    type: NodeType.END,
    position,
    inputPorts,
    outputPorts: []
  };
}

// 创建处理节点
export function createProcessNode(position: { x: number; y: number }): GameNode {
  const nodeId = uuidv4();
  const inputPorts: Port[] = [];
  const outputPorts: Port[] = [];
  
  // 处理节点有1-2个输入端口和1-2个输出端口
  const inputPortCount = Math.floor(Math.random() * 2) + 1;
  const outputPortCount = Math.floor(Math.random() * 2) + 1;
  
  // 创建输入端口
  for (let i = 0; i < inputPortCount; i++) {
    const portPosition = {
      x: position.x, // 节点左侧
      y: position.y + 20 + i * 30
    };
    
    inputPorts.push(createPort(
      nodeId,
      PortDirection.INPUT,
      getRandomPortShape(),
      getRandomPortColor(),
      portPosition
    ));
  }
  
  // 创建输出端口
  for (let i = 0; i < outputPortCount; i++) {
    const portPosition = {
      x: position.x + 120, // 节点右侧
      y: position.y + 20 + i * 30
    };
    
    outputPorts.push(createPort(
      nodeId,
      PortDirection.OUTPUT,
      getRandomPortShape(),
      getRandomPortColor(),
      portPosition
    ));
  }
  
  return {
    id: nodeId,
    type: NodeType.PROCESS,
    position,
    inputPorts,
    outputPorts
  };
}

// 生成兼容的节点（确保可以与现有节点连接）
export function generateCompatibleNode(existingNodes: GameNode[]): GameNode {
  const position = { x: 0, y: 0 }; // 临时位置，稍后会被设置
  
  // 收集所有未连接的端口
  const unconnectedPorts: Port[] = [];
  existingNodes.forEach(node => {
    node.inputPorts.forEach(port => {
      if (!port.isConnected) unconnectedPorts.push(port);
    });
    node.outputPorts.forEach(port => {
      if (!port.isConnected) unconnectedPorts.push(port);
    });
  });
  
  if (unconnectedPorts.length === 0) {
    // 如果没有未连接的端口，生成随机节点
    return createProcessNode(position);
  }
  
  // 随机选择一个未连接的端口
  const targetPort = unconnectedPorts[Math.floor(Math.random() * unconnectedPorts.length)];
  
  // 生成一个可以与目标端口连接的节点
  const nodeId = uuidv4();
  const inputPorts: Port[] = [];
  const outputPorts: Port[] = [];
  
  if (targetPort.direction === PortDirection.OUTPUT) {
    // 目标是输出端口，我们需要创建一个有匹配输入端口的节点
    const matchingInputPort = createPort(
      nodeId,
      PortDirection.INPUT,
      targetPort.shape,
      targetPort.color,
      { x: position.x, y: position.y + 20 }
    );
    inputPorts.push(matchingInputPort);
    
    // 添加一些随机输出端口
    const outputPortCount = Math.floor(Math.random() * 2) + 1;
    for (let i = 0; i < outputPortCount; i++) {
      outputPorts.push(createPort(
        nodeId,
        PortDirection.OUTPUT,
        getRandomPortShape(),
        getRandomPortColor(),
        { x: position.x + 120, y: position.y + 20 + i * 30 }
      ));
    }
  } else {
    // 目标是输入端口，我们需要创建一个有匹配输出端口的节点
    const matchingOutputPort = createPort(
      nodeId,
      PortDirection.OUTPUT,
      targetPort.shape,
      targetPort.color,
      { x: position.x + 120, y: position.y + 20 }
    );
    outputPorts.push(matchingOutputPort);
    
    // 添加一些随机输入端口
    const inputPortCount = Math.floor(Math.random() * 2) + 1;
    for (let i = 0; i < inputPortCount; i++) {
      inputPorts.push(createPort(
        nodeId,
        PortDirection.INPUT,
        getRandomPortShape(),
        getRandomPortColor(),
        { x: position.x, y: position.y + 20 + i * 30 }
      ));
    }
  }
  
  return {
    id: nodeId,
    type: NodeType.PROCESS,
    position,
    inputPorts,
    outputPorts
  };
}
