import React from 'react';
import { GameState, GameMode } from '../types';

interface GameControlsProps {
  gameState: GameState;
  gameMode: GameMode;
  onGameModeChange: (mode: GameMode) => void;
  onStartGame: () => void;
  onPauseGame: () => void;
  onResetGame: () => void;
  onGenerateNode: () => void;
  onTestSolution: () => void;
  canGenerateNode: boolean;
  isSolvable: boolean;
  allPortsConnected: boolean;
}

const GameControls: React.FC<GameControlsProps> = ({
  gameState,
  gameMode,
  onGameModeChange,
  onStartGame,
  onPauseGame,
  onResetGame,
  onGenerateNode,
  onTestSolution,
  canGenerateNode,
  isSolvable,
  allPortsConnected
}) => {
  return (
    <div className="game-controls">
      <div className="game-info">
        <h2>蓝图连接游戏</h2>
        <div className="score-info">
          <p>分数: {gameState.score}</p>
          <p>等级: {gameState.level}</p>
        </div>
      </div>
      
      <div className="game-mode-selector">
        <h3>游戏模式</h3>
        <div className="mode-buttons">
          <button
            className={gameMode === GameMode.TETRIS ? 'active' : ''}
            onClick={() => onGameModeChange(GameMode.TETRIS)}
            disabled={gameState.isGameRunning}
          >
            俄罗斯方块模式
          </button>
          <button
            className={gameMode === GameMode.TURN_BASED ? 'active' : ''}
            onClick={() => onGameModeChange(GameMode.TURN_BASED)}
            disabled={gameState.isGameRunning}
          >
            回合制模式
          </button>
        </div>
      </div>
      
      <div className="game-actions">
        <h3>游戏控制</h3>
        <div className="action-buttons">
          {!gameState.isGameRunning ? (
            <button onClick={onStartGame} className="start-button">
              开始游戏
            </button>
          ) : (
            <button onClick={onPauseGame} className="pause-button">
              暂停游戏
            </button>
          )}
          
          <button onClick={onResetGame} className="reset-button">
            重置游戏
          </button>
          
          {gameMode === GameMode.TURN_BASED && (
            <button
              onClick={onGenerateNode}
              disabled={!canGenerateNode}
              className="generate-button"
            >
              生成新节点
            </button>
          )}
          
          <button
            onClick={onTestSolution}
            className="test-button"
            disabled={gameState.nodes.length === 0}
          >
            测试解决方案
          </button>
        </div>
      </div>
      
      <div className="game-status">
        <h3>游戏状态</h3>
        <div className="status-indicators">
          <div className={`status-item ${isSolvable ? 'valid' : 'invalid'}`}>
            <span className="status-icon">{isSolvable ? '✓' : '✗'}</span>
            <span>图结构有效</span>
          </div>
          
          <div className={`status-item ${allPortsConnected ? 'valid' : 'invalid'}`}>
            <span className="status-icon">{allPortsConnected ? '✓' : '✗'}</span>
            <span>所有端口已连接</span>
          </div>
          
          <div className={`status-item ${isSolvable && allPortsConnected ? 'valid' : 'invalid'}`}>
            <span className="status-icon">{isSolvable && allPortsConnected ? '✓' : '✗'}</span>
            <span>可解</span>
          </div>
        </div>
      </div>
      
      <div className="game-stats">
        <h3>统计信息</h3>
        <div className="stats-grid">
          <div className="stat-item">
            <span className="stat-label">节点数量:</span>
            <span className="stat-value">{gameState.nodes.length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">连接数量:</span>
            <span className="stat-value">{gameState.connections.length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">临时节点:</span>
            <span className="stat-value">{gameState.temporaryNodes.length}</span>
          </div>
        </div>
      </div>
      
      <div className="game-instructions">
        <h3>游戏说明</h3>
        <ul>
          <li>拖拽节点到游戏区域进行放置</li>
          <li>从输出端口拖拽到输入端口创建连接</li>
          <li>相同形状和颜色的端口才能连接</li>
          <li>点击连接线可以删除连接</li>
          <li>目标：连接所有节点形成有效的数据流</li>
        </ul>
      </div>
    </div>
  );
};

export default GameControls;
